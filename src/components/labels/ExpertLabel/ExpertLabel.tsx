import React, { FC } from "react";
import classNames from "classnames";
import Typography from "../../Typography";
import EngineerIcon from "@/assets/icons/EngineerIcon";
import * as styles from "../label.css";

interface ExpertLabelProps {
  className?: string;
  context: string;
}

const ExpertLabel: FC<ExpertLabelProps> = ({ className, context }) => {
  return (
    <div
      className={classNames(styles.root, className)}
    >
      <EngineerIcon />
      <Typography
        variant='bodyMedium'
        className={styles.text}
      >
        Expert Engineers in {context}
      </Typography>
    </div>
  );
};

export default ExpertLabel;
