import React, { <PERSON> } from "react";
import classNames from "classnames";
import Typography from "../../Typography";
import VettedIcon from "@/assets/icons/VettedIcon";
import * as styles from "../label.css";

interface VettedLabelProps {
  className?: string;
}

const VettedLabel: FC<VettedLabelProps> = ({ className }) => {
  return (
    <div
      className={classNames(styles.root, className)}
    >
      <VettedIcon />
      <Typography
        variant='bodyMedium'
        className={styles.text}
      >
        Vetted & Accredited
      </Typography>
    </div>
  );
};

export default VettedLabel;
