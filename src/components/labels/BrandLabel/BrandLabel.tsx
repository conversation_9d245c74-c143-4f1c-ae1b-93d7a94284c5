import React, { <PERSON> } from "react";
import classNames from "classnames";
import MemoStarIcon from "@/assets/icons/StarIcon";
import Typography from "../../Typography";
import * as styles from "../label.css";

interface BrandLabelProps {
  className?: string;
}

const BrandLabel: FC<BrandLabelProps> = ({ className }) => {
  return (
    <div
      className={classNames(styles.root, className)}
    >
      <MemoStarIcon />
      <Typography
        variant='bodyMedium'
        className={styles.text}
      >
        A Brand You Can Trust
      </Typography>
    </div>
  );
};

export default BrandLabel;
