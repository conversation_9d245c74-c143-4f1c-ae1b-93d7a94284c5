import React, { <PERSON> } from "react";
import classNames from "classnames";
import Typography from "../../Typography";
import TimeFilledIcon from "@/assets/icons/TimeIcon copy";
import * as styles from "../label.css";

interface MoneyBackLabel {
  className?: string;
}

const MoneyBackLabel: FC<MoneyBackLabel> = ({ className }) => {
  return (
    <div
      className={classNames(styles.root, className)}
    >
      <TimeFilledIcon />
      <Typography
        variant='bodyMedium'
        className={styles.text}
      >
        1hr Guarantee or Money Back
      </Typography>
    </div>
  );
};

export default MoneyBackLabel;
