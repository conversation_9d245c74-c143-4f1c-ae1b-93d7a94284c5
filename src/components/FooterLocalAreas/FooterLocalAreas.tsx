"use client";

import MemoCheckMarkIcon from "@/assets/icons/CheckMarkIcon";
import MemoEmailIcon from "@/assets/icons/EmailIcon";
import MemoMarkerLocationIcon from "@/assets/icons/MarkerLocationIcon";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import useStore from "@/hooks/useStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import axios from "axios";
import classNames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { FooterDocumentDataMenuItem, Simplify } from "prismicio-types";
import { Controller, useForm } from "react-hook-form";
import Container from "../Container";
import IconButton from "../IconButton";
import PrismicLink from "../Link";
import Loader from "../Loader";
import Logo from "../Logo/Logo";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./FooterLocalAreas.css";
import GasSafeRegisterImage from "./GasSafeRegister.png";
import {
  ACCREDITATIONS_ITEMS,
  EMAIL,
  FACEBOOK_LINK,
  INSTAGRAM_LINK,
  LINKEDIN_LINK,
  MENU,
  PHONE_NUMBER,
  YOUTUBE_LINK,
} from "./congif";
import { WhatsAppButtonOutlined } from "../ContactButtons";

interface FormValues {
  email: string;
}

const FooterLocalAreas = () => {
  const {
    landign: { setCallUsModalIsOpen },
  } = useStore();

  const form = useForm<FormValues>({
    mode: "onBlur",
    defaultValues: {
      email: "",
    },
  });

  const handleSubscribeEmail = async ({ email }: FormValues) => {
    try {
      await axios.post("/api/subscribe-email", {
        email,
      });

      setTimeout(() => {
        form.reset();
      }, 3000);
    } catch (error) {}
  };

  return (
    <Container
      className={styles.container}
      notFullHeight
      removeBorderRadius
    >
      <footer
        className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
      >
        <div
          className={gridSprinkle({
            type: "item",
            cols: { mobile: 10, tablet: 6 },
          })}
        >
          <div
            className={classNames(styles.contactInfo)}
          >
            <Logo
              className={styles.logo}
            />
            <Typography
              as={Link}
              href={`tel:${(PHONE_NUMBER || "").replaceAll(" ", "")}`}
              className={styles.menuLink.hideOnTablet}
            >
              <MemoPhoneIcon
                className={styles.contactInfoItemIcon}
              />
              <span>{PHONE_NUMBER}</span>
            </Typography>
            <Typography
              as={"button"}
              onClick={() => setCallUsModalIsOpen(true)}
              className={classNames(
                styles.menuLink.hideOnMobile,
                styles.phoneButtom
              )}
            >
              <MemoPhoneIcon
                className={styles.contactInfoItemIcon}
              />
              <span>{PHONE_NUMBER}</span>
            </Typography>
            <Typography
              as={Link}
              href={`mailto:${EMAIL}`}
              className={styles.menuLink.default}
            >
              <MemoEmailIcon
                className={styles.contactInfoItemIcon}
              />
              <span>{EMAIL}</span>
            </Typography>
            <Typography
              className={styles.menuLink.default}
            >
              <MemoMarkerLocationIcon
                className={styles.contactInfoItemIcon}
              />
              <span>
                Level 30, The Leadenhall Building, 122 Leadenhall Street,
                London, EC3V 4AB
              </span>
            </Typography>
            <WhatsAppButtonOutlined
              size='small'
              className={styles.whatsAppBtn}
            />
          </div>
        </div>
        <div
          className={gridSprinkle({
            type: "item",
            cols: { mobile: 5, tablet: 2 },
          })}
        >
          <Typography
            className={styles.menuTitle}
          >Menu</Typography>
          <div
            className={styles.menu}
          >
            {MENU.filter((link) => !link.parent_menu_item_name).map(
              (link, index) => (
                <Typography
                  key={index}
                  as={PrismicLink}
                  field={link.menu_item_link}
                  className={styles.menuLink.default}
                >
                  {link.menu_item_name}
                </Typography>
              )
            )}
          </div>
        </div>
        {Object.entries(
          MENU.filter((link) => link.parent_menu_item_name).reduce(
            (accum, link) => {
              if (!accum[String(link.parent_menu_item_name)]) {
                accum[String(link.parent_menu_item_name)] = [];
              }

              accum[String(link.parent_menu_item_name)].push(link);

              return accum;
            },
            {} as any
          )
        ).map(([parentLinkName, subLinks]) => (
          <div
            key={parentLinkName}
            className={gridSprinkle({
              type: "item",
              cols: { mobile: 5, tablet: 2 },
            })}
          >
            <Typography
              className={styles.menuTitle}
            >
              {parentLinkName}
            </Typography>
            <div
              className={styles.menu}
            >
              {(subLinks as Simplify<FooterDocumentDataMenuItem>[]).map(
                (subLink) => (
                  <Typography
                    key={subLink.menu_item_name}
                    as={PrismicLink}
                    field={subLink.menu_item_link}
                    className={styles.menuLink.default}
                  >
                    {subLink.menu_item_name}
                  </Typography>
                )
              )}
            </div>
          </div>
        ))}
        <form
          onSubmit={form.handleSubmit(handleSubscribeEmail)}
          className={classNames(
            styles.subscribeField,
            gridSprinkle({
              type: "item",
              cols: { mobile: 10, tablet: 6 },
              alignSelf: "end",
            })
          )}
        >
          <Controller
            control={form.control}
            name='email'
            rules={{
              required: "Email is required",
              validate: (value) => {
                if (
                  value.trim() &&
                  !/^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i.test(
                    value
                  )
                )
                  return "Incorrect email";
              },
            }}
            render={({ field, fieldState }) => (
              <>
                {form.formState.isSubmitSuccessful && (
                  <Typography
                    className={styles.subscribeFieldSuccessLabel}
                    variant='noteMedium'
                  >
                    Thank you for subscribing!
                  </Typography>
                )}
                <TextInput
                  {...field}
                  disabled={form.formState.isSubmitSuccessful}
                  error={fieldState.error?.message}
                  label={
                    <Typography
                      variant='bodyMedium'
                      className={styles.formTitle}
                    >
                      Subscribe to our newsletter
                    </Typography>
                  }
                  variant='filled'
                  placeholder='Enter your email'
                  inputClassname={styles.input}
                />
                <IconButton
                  title='Subscribe to our newsletter'
                  type='submit'
                  disabled={Boolean(fieldState.error?.message)}
                  color='secondary'
                  shape='rect'
                >
                  {form.formState.isSubmitting ? (
                    <div
                      className={styles.loaderWrapper}
                    >
                      <Loader />
                    </div>
                  ) : (
                    <>
                      {form.formState.isSubmitSuccessful ? (
                        <MemoCheckMarkIcon />
                      ) : (
                        <NextArrowIcon />
                      )}
                    </>
                  )}
                </IconButton>
              </>
            )}
          />
        </form>
        <div
          className={classNames(
            styles.socials,
            gridSprinkle({
              type: "item",
              cols: { mobile: 10, tablet: 4 },
              alignSelf: "end",
              justifyContent: { mobile: "space-between", tablet: "flex-start" },
            })
          )}
        >
          <IconButton
            title='Our Facebook'
            as={PrismicNextLink}
            shape='box'
            field={FACEBOOK_LINK}
            color='primaryInverted'
            className={styles.socialLink}
          >
            <svg
              width='16'
              height='28'
              viewBox='0 0 16 28'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M10.2768 28V15.2279H14.7796L15.4538 10.2503H10.2766V7.07238C10.2766 5.63126 10.6969 4.64922 12.8676 4.64922L15.636 4.64799V0.196112C15.1572 0.13556 13.5137 0 11.602 0C7.61039 0 4.87768 2.31961 4.87768 6.57957V10.2503H0.363281V15.2279H4.87768V27.9999H10.2768V28Z'
                fill='currentColor'
              />
            </svg>
          </IconButton>

          <IconButton
            title='Our Instagram'
            as={PrismicNextLink}
            shape='box'
            field={INSTAGRAM_LINK}
            color='primaryInverted'
            className={styles.socialLink}
          >
            <svg
              width='28'
              height='28'
              viewBox='0 0 28 28'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M0 14C0 8.40756 0 5.61134 1.33263 3.5973C1.92772 2.69794 2.69794 1.92772 3.5973 1.33263C5.61134 0 8.40756 0 14 0C19.5924 0 22.3887 0 24.4027 1.33263C25.3021 1.92772 26.0723 2.69794 26.6674 3.5973C28 5.61134 28 8.40756 28 14C28 19.5924 28 22.3887 26.6674 24.4027C26.0723 25.3021 25.3021 26.0723 24.4027 26.6674C22.3887 28 19.5924 28 14 28C8.40756 28 5.61134 28 3.5973 26.6674C2.69794 26.0723 1.92772 25.3021 1.33263 24.4027C0 22.3887 0 19.5924 0 14ZM21.2479 14.0001C21.2479 18.003 18.0028 21.248 13.9999 21.248C9.99694 21.248 6.75191 18.003 6.75191 14.0001C6.75191 9.99714 9.99694 6.75211 13.9999 6.75211C18.0028 6.75211 21.2479 9.99714 21.2479 14.0001ZM13.9999 18.7959C16.6485 18.7959 18.7957 16.6487 18.7957 14.0001C18.7957 11.3514 16.6485 9.2043 13.9999 9.2043C11.3512 9.2043 9.2041 11.3514 9.2041 14.0001C9.2041 16.6487 11.3512 18.7959 13.9999 18.7959ZM21.5341 8.09102C22.4747 8.09102 23.2372 7.32854 23.2372 6.38796C23.2372 5.44739 22.4747 4.68491 21.5341 4.68491C20.5935 4.68491 19.8311 5.44739 19.8311 6.38796C19.8311 7.32854 20.5935 8.09102 21.5341 8.09102Z'
                fill='currentColor'
              />
            </svg>
          </IconButton>

          <IconButton
            title='Our LinkedIn'
            as={PrismicNextLink}
            shape='box'
            field={LINKEDIN_LINK}
            color='primaryInverted'
            className={styles.socialLink}
          >
            <svg
              width='28'
              height='28'
              viewBox='0 0 28 28'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M0 3.76439C0 2.8665 0.315324 2.12576 0.945946 1.54217C1.57657 0.958546 2.39641 0.666748 3.40541 0.666748C4.39641 0.666748 5.19819 0.954048 5.81081 1.5287C6.44143 2.12129 6.75676 2.89344 6.75676 3.8452C6.75676 4.70715 6.45046 5.42543 5.83784 6.00008C5.20722 6.59267 4.37838 6.88897 3.35135 6.88897H3.32432C2.33332 6.88897 1.53154 6.59267 0.918919 6.00008C0.306297 5.40749 0 4.66225 0 3.76439ZM0.351351 27.3334V9.34015H6.35135V27.3334H0.351351ZM9.67568 27.3334H15.6757V17.2863C15.6757 16.6577 15.7478 16.1729 15.8919 15.8317C16.1441 15.2212 16.527 14.7049 17.0405 14.2829C17.5541 13.8609 18.1982 13.6499 18.973 13.6499C20.991 13.6499 22 15.0057 22 17.7172V27.3334H28V17.0169C28 14.3592 27.3694 12.3435 26.1081 10.9698C24.8468 9.59604 23.1802 8.90917 21.1081 8.90917C18.7838 8.90917 16.973 9.9058 15.6757 11.8991V11.9529H15.6486L15.6757 11.8991V9.34015H9.67568C9.7117 9.91477 9.72973 11.7015 9.72973 14.7004C9.72973 17.6993 9.7117 21.9103 9.67568 27.3334Z'
                fill='currentColor'
              />
            </svg>
          </IconButton>

          <IconButton
            title='Our Youtube'
            as={PrismicNextLink}
            shape='box'
            field={YOUTUBE_LINK}
            color='primaryInverted'
            className={styles.socialLink}
          >
            <svg
              width='32'
              height='22'
              viewBox='0 0 32 22'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M16.682 21.8572L10.363 21.7391C8.31706 21.6979 6.26602 21.7801 4.2602 21.3537C1.20888 20.7167 0.99271 17.5933 0.766515 14.9734C0.45484 11.2901 0.575499 7.53989 1.16367 3.88733C1.49571 1.83786 2.80243 0.614931 4.82322 0.481852C11.6449 -0.0011085 18.5119 0.0561275 25.3184 0.281446C26.0373 0.302102 26.7611 0.414998 27.4699 0.543503C30.9688 1.17026 31.0542 4.70977 31.281 7.68937C31.5072 10.6997 31.4117 13.7255 30.9793 16.7154C30.6325 19.1909 29.9689 21.2668 27.1683 21.4672C23.6593 21.7293 20.2309 21.9403 16.7121 21.8731C16.7122 21.8572 16.692 21.8572 16.682 21.8572ZM12.967 15.5899C15.6113 14.0383 18.2052 12.5127 20.8343 10.9716C18.1851 9.42002 15.5962 7.89435 12.967 6.35323V15.5899Z'
                fill='currentColor'
              />
            </svg>
          </IconButton>

          <Image
            src={GasSafeRegisterImage}
            alt='gas safe register'
            width={75}
            height={103}
            className={styles.gasSafeRegisterImage}
            style={{ marginLeft: "auto" }}
          />
        </div>

        <div
          className={classNames(
            styles.accreditationsTitleWrapper,
            gridSprinkle({ type: "item", cols: 10 })
          )}
        >
          <Typography
            variant='bodyMedium'
            className={styles.accreditationsTitle}
          >
            {/* <PrismicRichText field={footer.data.accreditations_title} /> */}
            Vetted and Accredited by
          </Typography>
        </div>

        <div
          className={classNames(
            styles.accreditations,
            gridSprinkle({ type: "item", cols: 10 })
          )}
        >
          {ACCREDITATIONS_ITEMS.map((item) => (
            <div
              className={styles.accreditationContainer}
              key={item.accreditation_image.id}
            >
              <div
                className={styles.accreditationWrapper}
              >
                <PrismicNextLink
                  field={item.accreditation_link}
                  className={styles.accreditationImageWr}
                >
                  <PrismicNextImage
                    className={styles.accreditationImage}
                    field={item.accreditation_image}
                  />
                </PrismicNextLink>
              </div>
            </div>
          ))}
        </div>

        <Image
          src={GasSafeRegisterImage}
          alt='gas safe register'
          width={75}
          height={103}
          className={classNames(
            styles.gasSafeRegisterImageMobile,
            gridSprinkle({ type: "item", cols: { mobile: 10 } })
          )}
        />

        <div
          className={classNames(
            styles.divider,
            gridSprinkle({ type: "item", cols: { mobile: 10 } })
          )}
        />

        <div
          className={gridSprinkle({
            type: "item",
            cols: { mobile: 5, tablet: 3 },
            display: { mobile: "none", tablet: "flex" },
          })}
        ></div>
        <div
          className={gridSprinkle({
            type: "item",
            cols: { mobile: 5, tablet: 3 },
            display: { mobile: "none", tablet: "flex" },
          })}
        ></div>
        <div
          className={classNames(
            styles.copyright,
            gridSprinkle({
              type: "item",
              cols: { mobile: 10, tablet: 4 },
            })
          )}
        >
          <Typography
            variant='note'
            className={gridSprinkle({ display: { tablet: "none" } })}
          >
            All Rights Reserved
          </Typography>
          <Typography
            variant='note'
          >
            Copyright © {new Date().getFullYear()} Pleasant Plumbers
          </Typography>
        </div>
      </footer>
    </Container>
  );
};

export default FooterLocalAreas;
