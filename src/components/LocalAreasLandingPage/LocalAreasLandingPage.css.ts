import { mode } from "@/styles/functions.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  display: "flex",
  flexDirection: "column",

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.castletonGreen,
    },
  },
});

export const main = style({
  display: "flex",
  flexDirection: "column",
  flex: "1 1",
});

export const link = style({
  textDecoration: "none",
  fontWeight: 400,
});
