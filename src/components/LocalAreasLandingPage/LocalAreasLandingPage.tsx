"use client";
import { FC } from "react";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import classNames from "classnames";
import { components } from "@/slices";
import FloatingContactWidgets from "../FloatingContactWidgets";
import Props from "./LocalAreasLandingPage.types";
import { WHATS_UP_NUMBER, HEADER_PHONE_NUMBER } from "@/utils/constants";
import { SliceZone } from "@prismicio/react";
import HeaderSection from "../ProtectYourBusiness/components/Header/HeaderSection";
import FooterLocalAreas from "../FooterLocalAreas";
import BookingModal from "../BookingModal";
import * as styles from "./LocalAreasLandingPage.css";

const LocalAreasLandingPage: FC<Props> = observer(
  ({ page, searchParams, slicesData }) => {
    const { landign } = useStore();

    return (
      <>
        <div
          className={styles.root}
        >
          <HeaderSection
            buttonText='Message Us'
            buttonAction={() => landign.setBookingModalIsOpen(true)}
          />
          <main
            className={classNames(styles.main, `main-${page.uid}`)}
          >
            <SliceZone
              slices={
                searchParams.mode === "commercial"
                  ? page.data.slices2.length
                    ? page.data.slices2
                    : page.data.slices
                  : page.data.slices
              }
              components={components}
              context={slicesData}
            />
          </main>
          <FooterLocalAreas />
        </div>

        <FloatingContactWidgets
          phoneNumber={HEADER_PHONE_NUMBER}
          whatsAppNumber={WHATS_UP_NUMBER}
        />

        <BookingModal
          isOpen={landign.bookingModalIsOpen}
          onClose={() => landign.setBookingModalIsOpen(false)}
        />
      </>
    );
  }
);

export default LocalAreasLandingPage;
