import * as React from "react";

function TimeFilledIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='29'
      height='29'
      viewBox='0 0 29 29'
      fill='none'
      {...props}
    >
      <path
        d='M14.0234 3C18.2891 3 22.2266 5.29688 24.3828 9C26.5391 12.75 26.5391 17.2969 24.3828 21C22.2266 24.75 18.2891 27 14.0234 27C9.71094 27 5.77344 24.75 3.61719 21C1.46094 17.2969 1.46094 12.75 3.61719 9C5.77344 5.29688 9.71094 3 14.0234 3ZM12.8984 8.625V15C12.8984 15.375 13.0859 15.75 13.3672 15.9375L17.8672 18.9375C18.3828 19.3125 19.0859 19.1719 19.4609 18.6562C19.7891 18.1406 19.6484 17.4375 19.1328 17.0625L15.1484 14.4375V8.625C15.1484 8.01562 14.6328 7.5 14.0234 7.5C13.3672 7.5 12.8984 8.01562 12.8984 8.625Z'
        fill='currentColor'
      />
    </svg>
  );
}

export default TimeFilledIcon;
