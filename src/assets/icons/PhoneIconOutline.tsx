import * as React from "react";

function PhoneIconOutline(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='27'
      height='26'
      viewBox='0 0 27 26'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2429)'
      >
        <path
          d='M20.798 16.1056C20.2657 15.5514 19.6237 15.255 18.9433 15.255C18.2683 15.255 17.6208 15.5459 17.0665 16.1001L15.3325 17.8287C15.1898 17.7518 15.0471 17.6805 14.91 17.6092C14.7124 17.5104 14.5258 17.4171 14.3667 17.3183C12.7424 16.2867 11.2663 14.9423 9.8505 13.2027C9.16457 12.3357 8.70362 11.6059 8.36889 10.8651C8.81886 10.4535 9.23591 10.0255 9.64198 9.61391C9.79563 9.46026 9.94928 9.30112 10.1029 9.14747C11.2553 7.9951 11.2553 6.50251 10.1029 5.35014L8.60485 3.85206C8.43474 3.68195 8.25914 3.50635 8.09451 3.33075C7.76526 2.99052 7.41955 2.63933 7.06287 2.31008C6.53058 1.78328 5.89403 1.50342 5.22456 1.50342C4.55509 1.50342 3.90757 1.78328 3.35882 2.31008C3.35333 2.31556 3.35333 2.31556 3.34784 2.32105L1.4821 4.20326C0.779703 4.90565 0.379118 5.7617 0.291318 6.75493C0.159618 8.35728 0.631542 9.84987 0.993715 10.8266C1.88269 13.2247 3.21066 15.4471 5.19164 17.8287C7.59515 20.6986 10.4871 22.9649 13.7905 24.5618C15.0526 25.1599 16.7373 25.8678 18.6195 25.9885C18.7347 25.994 18.8555 25.9995 18.9652 25.9995C20.2328 25.9995 21.2974 25.5441 22.1315 24.6386C22.137 24.6277 22.1479 24.6222 22.1534 24.6112C22.4388 24.2655 22.768 23.9527 23.1137 23.618C23.3497 23.393 23.5911 23.157 23.8271 22.9101C24.3704 22.3449 24.6557 21.6864 24.6557 21.0114C24.6557 20.331 24.3649 19.6779 23.8106 19.1292L20.798 16.1056ZM22.7625 21.8839C22.7571 21.8839 22.7571 21.8894 22.7625 21.8839C22.5485 22.1144 22.329 22.3229 22.0931 22.5534C21.7364 22.8936 21.3742 23.2503 21.034 23.6509C20.4797 24.2435 19.8267 24.5234 18.9707 24.5234C18.8884 24.5234 18.8006 24.5234 18.7183 24.5179C17.0885 24.4136 15.5739 23.7771 14.438 23.2338C11.3321 21.7303 8.60485 19.5956 6.33852 16.8903C4.46729 14.635 3.21614 12.5497 2.38753 10.3108C1.8772 8.94444 1.69062 7.87987 1.77294 6.87566C1.82781 6.23362 2.07475 5.70134 2.53021 5.24588L4.40144 3.37465C4.67033 3.12222 4.95567 2.98504 5.23554 2.98504C5.58125 2.98504 5.86111 3.19356 6.03671 3.36916C6.04219 3.37465 6.04768 3.38014 6.05317 3.38562C6.38791 3.69841 6.70618 4.02217 7.04092 4.36788C7.21103 4.54348 7.38663 4.71908 7.56223 4.90017L9.06031 6.39825C9.64198 6.97992 9.64198 7.51769 9.06031 8.09937C8.90117 8.2585 8.74752 8.41764 8.58838 8.57129C8.12744 9.04321 7.68844 9.48221 7.21103 9.91023C7.20005 9.92121 7.18908 9.9267 7.18359 9.93767C6.71167 10.4096 6.79947 10.8705 6.89824 11.1833C6.90373 11.1998 6.90922 11.2163 6.9147 11.2327C7.30431 12.1766 7.85306 13.0655 8.68716 14.1246L8.69265 14.1301C10.2072 15.9958 11.804 17.45 13.5655 18.564C13.7905 18.7067 14.021 18.8219 14.2405 18.9316C14.438 19.0304 14.6246 19.1237 14.7837 19.2225C14.8057 19.2335 14.8276 19.2499 14.8496 19.2609C15.0362 19.3542 15.2118 19.3981 15.3929 19.3981C15.8483 19.3981 16.1337 19.1127 16.227 19.0194L18.1037 17.1427C18.2902 16.9562 18.5866 16.7312 18.9323 16.7312C19.2725 16.7312 19.5524 16.9452 19.7225 17.1318C19.728 17.1372 19.728 17.1372 19.7335 17.1427L22.7571 20.1663C23.3223 20.7261 23.3223 21.3022 22.7625 21.8839Z'
          fill='currentColor'
        />
        <path
          d='M14.3008 6.18443C15.7385 6.42588 17.0446 7.10633 18.0872 8.14895C19.1298 9.19157 19.8048 10.4976 20.0517 11.9353C20.1121 12.2975 20.4248 12.5499 20.7815 12.5499C20.8254 12.5499 20.8638 12.5444 20.9077 12.5389C21.3138 12.4731 21.5827 12.089 21.5169 11.6829C21.2205 9.94335 20.3974 8.35747 19.1408 7.10084C17.8841 5.84421 16.2983 5.02109 14.5587 4.72476C14.1527 4.65891 13.774 4.9278 13.7027 5.32838C13.6313 5.72897 13.8947 6.11858 14.3008 6.18443Z'
          fill='currentColor'
        />
        <path
          d='M26.2205 11.4691C25.7321 8.60468 24.3822 5.99813 22.3079 3.92387C20.2336 1.8496 17.6271 0.49968 14.7626 0.011294C14.362 -0.0600432 13.9834 0.214331 13.9121 0.614917C13.8462 1.02099 14.1151 1.39963 14.5212 1.47096C17.0783 1.90447 19.4105 3.11721 21.2653 4.96649C23.1201 6.82125 24.3273 9.15343 24.7608 11.7106C24.8212 12.0728 25.134 12.3252 25.4907 12.3252C25.5346 12.3252 25.573 12.3197 25.6169 12.3142C26.0175 12.2539 26.2918 11.8697 26.2205 11.4691Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2429'
        >
          <rect
            width='26'
            height='26'
            fill='white'
            transform='translate(0.25)'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

const MemoPhoneIconOutline = React.memo(PhoneIconOutline);
export default MemoPhoneIconOutline;
