import * as React from "react";

function HandShakeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2107)'
      >
        <path
          d='M20 6.66669C20 7.55335 19.7867 8.50419 19.365 9.49335C19.23 9.81002 18.9225 10.0009 18.5983 10.0009C18.4892 10.0009 18.3783 9.97919 18.2717 9.93335C17.8475 9.75335 17.6508 9.26335 17.8317 8.84002C18.1642 8.05835 18.3333 7.32752 18.3333 6.66669C18.3333 4.40835 16.6158 2.50002 14.5833 2.50002C12.7208 2.50002 11.66 3.25419 10.945 3.91169L7.75833 6.86919C7.46417 7.16419 7.39417 7.66002 7.6225 7.97752C7.77167 8.18502 7.99083 8.31002 8.23833 8.33002C8.48417 8.35252 8.7225 8.26252 8.895 8.08835L12.3725 4.75919C12.7025 4.44169 13.2317 4.45252 13.5508 4.78502C13.8692 5.11752 13.8575 5.64502 13.525 5.96335L12.4933 6.95085L16.4492 10.4225C17.3758 11.2359 17.6042 12.6017 16.98 13.5992C16.5958 14.2125 15.9683 14.625 15.2592 14.7292C15.2108 14.7359 15.1633 14.7417 15.115 14.7459C15.1117 15.175 14.9975 15.6 14.7633 15.975C14.3792 16.5884 13.7517 17.0009 13.0425 17.105C12.8883 17.1267 12.735 17.1359 12.5817 17.1284C12.5367 17.4467 12.4283 17.7567 12.2525 18.0375C11.8683 18.6509 11.2492 19.1934 10.1758 19.1934C9.58667 19.1934 9.015 18.9809 8.56667 18.5867L5.98 16.2667C3.25083 13.8 0 10.1342 0 6.66669C0 3.45002 2.43 0.833353 5.41667 0.833353C6.105 0.833353 6.7775 0.973353 7.41417 1.25002C7.83667 1.43335 8.03083 1.92419 7.8475 2.34585C7.66417 2.76752 7.17333 2.96085 6.75167 2.77919C6.325 2.59419 5.87583 2.50085 5.41583 2.50085C3.38333 2.50085 1.66583 4.40919 1.66583 6.66752C1.66583 8.89585 3.59333 11.865 7.095 15.0292L9.67333 17.3409C9.8375 17.485 10.065 17.5517 10.29 17.5192C10.5183 17.4859 10.7142 17.3567 10.84 17.1542C11.0317 16.8467 10.9367 16.39 10.6225 16.115L7.95 13.5742C7.6175 13.2575 7.60333 12.7309 7.91917 12.3967C8.23583 12.0634 8.7625 12.0492 9.09667 12.3642L12.1983 15.2925C12.3475 15.4225 12.5783 15.4909 12.8 15.4559C13.0283 15.4225 13.2242 15.2925 13.35 15.0909C13.5417 14.7834 13.4467 14.3267 13.1325 14.0509L10.1658 11.365C9.82583 11.0575 9.79833 10.5317 10.105 10.1909C10.4108 9.85002 10.9367 9.82002 11.2792 10.125L14.3975 12.9C14.5642 13.0467 14.7925 13.115 15.0167 13.08C15.245 13.0467 15.4408 12.9167 15.5667 12.715C15.7583 12.4075 15.6633 11.9509 15.3492 11.675L11.2842 8.10752L10.0592 9.28002C9.55333 9.78669 8.83917 10.05 8.1025 9.99169C7.37083 9.93252 6.70167 9.55335 6.26833 8.95085C5.56417 7.97252 5.70667 6.56169 6.6 5.66835L9.8125 2.68669C10.6808 1.88919 12.1158 0.83252 14.5817 0.83252C17.5683 0.83252 19.9983 3.44919 19.9983 6.66585L20 6.66669Z'
          fill='currentcolor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2107'
        >
          <rect
            width='20'
            height='20'
            fill='white'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default HandShakeIcon;
