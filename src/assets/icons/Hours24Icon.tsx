import * as React from "react";

function Hours24Icon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2146)'
      >
        <path
          d='M12.1667 17.75C11.9167 17.9383 11.6508 18.1366 11.425 18.3333H13.3333C13.5543 18.3333 13.7663 18.4211 13.9226 18.5774C14.0789 18.7337 14.1667 18.9456 14.1667 19.1666C14.1667 19.3876 14.0789 19.5996 13.9226 19.7559C13.7663 19.9122 13.5543 20 13.3333 20H10C9.77899 20 9.56702 19.9122 9.41074 19.7559C9.25446 19.5996 9.16667 19.3876 9.16667 19.1666C9.16667 17.9166 10.2292 17.12 11.1667 16.4166C11.7917 15.9483 12.5 15.4166 12.5 15C12.5 14.7789 12.4122 14.567 12.2559 14.4107C12.0996 14.2544 11.8877 14.1666 11.6667 14.1666C11.4457 14.1666 11.2337 14.2544 11.0774 14.4107C10.9211 14.567 10.8333 14.7789 10.8333 15C10.8333 15.221 10.7455 15.4329 10.5893 15.5892C10.433 15.7455 10.221 15.8333 10 15.8333C9.77899 15.8333 9.56702 15.7455 9.41074 15.5892C9.25446 15.4329 9.16667 15.221 9.16667 15C9.16667 14.3369 9.43006 13.701 9.8989 13.2322C10.3677 12.7634 11.0036 12.5 11.6667 12.5C12.3297 12.5 12.9656 12.7634 13.4344 13.2322C13.9033 13.701 14.1667 14.3369 14.1667 15C14.1667 16.25 13.1042 17.0466 12.1667 17.75ZM19.1667 12.5C18.9457 12.5 18.7337 12.5878 18.5774 12.744C18.4211 12.9003 18.3333 13.1123 18.3333 13.3333V15.8333H17.5C17.279 15.8333 17.067 15.7455 16.9107 15.5892C16.7545 15.4329 16.6667 15.221 16.6667 15V13.3333C16.6667 13.1123 16.5789 12.9003 16.4226 12.744C16.2663 12.5878 16.0543 12.5 15.8333 12.5C15.6123 12.5 15.4004 12.5878 15.2441 12.744C15.0878 12.9003 15 13.1123 15 13.3333V15C15 15.663 15.2634 16.2989 15.7322 16.7677C16.2011 17.2366 16.837 17.5 17.5 17.5H18.3333V19.1666C18.3333 19.3876 18.4211 19.5996 18.5774 19.7559C18.7337 19.9122 18.9457 20 19.1667 20C19.3877 20 19.5996 19.9122 19.7559 19.7559C19.9122 19.5996 20 19.3876 20 19.1666V13.3333C20 13.1123 19.9122 12.9003 19.7559 12.744C19.5996 12.5878 19.3877 12.5 19.1667 12.5ZM10.8333 9.99996V5.83329C10.8333 5.61228 10.7455 5.40032 10.5893 5.24404C10.433 5.08776 10.221 4.99996 10 4.99996C9.77899 4.99996 9.56702 5.08776 9.41074 5.24404C9.25446 5.40032 9.16667 5.61228 9.16667 5.83329V9.16663H6.66667C6.44565 9.16663 6.23369 9.25442 6.07741 9.4107C5.92113 9.56698 5.83333 9.77894 5.83333 9.99996C5.83333 10.221 5.92113 10.4329 6.07741 10.5892C6.23369 10.7455 6.44565 10.8333 6.66667 10.8333H10C10.221 10.8333 10.433 10.7455 10.5893 10.5892C10.7455 10.4329 10.8333 10.221 10.8333 9.99996ZM19.1667 1.66663C18.9457 1.66663 18.7337 1.75442 18.5774 1.9107C18.4211 2.06698 18.3333 2.27895 18.3333 2.49996V4.47829C17.4651 3.16794 16.3001 2.08073 14.933 1.30488C13.5659 0.52903 12.0351 0.0863962 10.4649 0.0128928C8.89472 -0.0606106 7.3293 0.237086 5.89569 0.881829C4.46208 1.52657 3.20065 2.5002 2.21376 3.72371C1.22687 4.94722 0.542318 6.38616 0.215643 7.92376C-0.111033 9.46136 -0.0706278 11.0543 0.33358 12.5734C0.737788 14.0924 1.49441 15.4948 2.54206 16.6667C3.58971 17.8386 4.89888 18.7471 6.36333 19.3183C6.46011 19.3556 6.56293 19.3748 6.66667 19.375C6.86113 19.3753 7.04958 19.3075 7.19937 19.1835C7.34917 19.0595 7.45087 18.887 7.48688 18.6959C7.52288 18.5048 7.49092 18.3071 7.39652 18.1371C7.30213 17.9671 7.15124 17.8354 6.97 17.765C5.72669 17.2842 4.61685 16.5122 3.73365 15.5137C2.85045 14.5153 2.21967 13.3195 1.89426 12.0268C1.56884 10.7341 1.55828 9.38219 1.86348 8.08457C2.16867 6.78695 2.7807 5.58148 3.6482 4.56935C4.5157 3.55722 5.61335 2.76797 6.84901 2.26785C8.08466 1.76774 9.42226 1.57134 10.7495 1.69516C12.0768 1.81898 13.355 2.2594 14.4768 2.97945C15.5986 3.6995 16.5314 4.67817 17.1967 5.83329H15C14.779 5.83329 14.567 5.92109 14.4107 6.07737C14.2545 6.23365 14.1667 6.44561 14.1667 6.66663C14.1667 6.88764 14.2545 7.0996 14.4107 7.25588C14.567 7.41216 14.779 7.49996 15 7.49996H17.5C18.163 7.49996 18.7989 7.23657 19.2678 6.76773C19.7366 6.29889 20 5.663 20 4.99996V2.49996C20 2.27895 19.9122 2.06698 19.7559 1.9107C19.5996 1.75442 19.3877 1.66663 19.1667 1.66663Z'
          fill='currentcolor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2146'
        >
          <rect
            width='20'
            height='20'
            fill='white'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default Hours24Icon;
