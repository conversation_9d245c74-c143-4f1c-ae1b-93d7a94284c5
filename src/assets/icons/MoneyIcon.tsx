import * as React from "react";

function MoneyIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2101)'
      >
        <path
          d='M20 4.16667V12.5C20 13.86 19.345 15.07 18.3333 15.8308V17.5C18.3333 17.96 17.96 18.3333 17.5 18.3333C17.04 18.3333 16.6667 17.96 16.6667 17.5V16.5825C16.3975 16.6375 16.1183 16.6667 15.8333 16.6667H12.5C12.04 16.6667 11.6667 16.2933 11.6667 15.8333C11.6667 15.3733 12.04 15 12.5 15H15.8333C17.2117 15 18.3333 13.8783 18.3333 12.5H17.5C17.04 12.5 16.6667 12.1267 16.6667 11.6667C16.6667 11.2067 17.04 10.8333 17.5 10.8333H18.3333V5.83333H17.5C17.04 5.83333 16.6667 5.46 16.6667 5C16.6667 4.54 17.04 4.16667 17.5 4.16667H18.3333C18.3333 2.78833 17.2117 1.66667 15.8333 1.66667H4.16667C2.78833 1.66667 1.66667 2.78833 1.66667 4.16667V5.83333C1.66667 6.29333 1.29333 6.66667 0.833333 6.66667C0.373333 6.66667 0 6.29333 0 5.83333V4.16667C0 1.86917 1.86917 0 4.16667 0H15.8333C18.1308 0 20 1.86917 20 4.16667ZM10 10.4167V17.0833C10 18.7458 7.85 20 5 20C2.15 20 0 18.7458 0 17.0833V10.4167C0 8.75417 2.15 7.5 5 7.5C7.85 7.5 10 8.75417 10 10.4167ZM8.33333 12.6267C7.46167 13.0708 6.30417 13.3333 5 13.3333C3.69583 13.3333 2.53833 13.0708 1.66667 12.6267V13.75C1.66667 14.1908 2.9325 15 5 15C7.0675 15 8.33333 14.1908 8.33333 13.75V12.6267ZM1.66667 10.4167C1.66667 10.8575 2.9325 11.6667 5 11.6667C7.0675 11.6667 8.33333 10.8575 8.33333 10.4167C8.33333 9.97583 7.0675 9.16667 5 9.16667C2.9325 9.16667 1.66667 9.97583 1.66667 10.4167ZM8.33333 17.0833V15.96C7.46167 16.4042 6.30417 16.6667 5 16.6667C3.69583 16.6667 2.53833 16.4042 1.66667 15.96V17.0833C1.66667 17.5242 2.9325 18.3333 5 18.3333C7.0675 18.3333 8.33333 17.5242 8.33333 17.0833ZM11.9992 11.4992C12.1492 11.6117 12.325 11.6667 12.4992 11.6667C12.7525 11.6667 13.0025 11.5517 13.1658 11.3342C13.8208 10.4633 14.1667 9.42583 14.1667 8.33333C14.1667 5.57583 11.9242 3.33333 9.16667 3.33333C8.07417 3.33333 7.03667 3.67917 6.16583 4.33417C5.79833 4.61083 5.72333 5.1325 6 5.50083C6.27667 5.86917 6.79833 5.94333 7.16667 5.66667C7.74667 5.23083 8.43833 5.00083 9.16667 5.00083C11.005 5.00083 12.5 6.49583 12.5 8.33417C12.5 9.0625 12.27 9.75417 11.8342 10.3333C11.5575 10.7008 11.6317 11.2233 12 11.5L11.9992 11.4992Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2101'
        >
          <rect
            width='20'
            height='20'
            fill='white'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default MoneyIcon;
