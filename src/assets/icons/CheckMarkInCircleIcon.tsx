import * as React from "react";

function CheckMarkInCircleIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2152)'
      >
        <path
          d='M10 20C8.645 20 7.36667 19.405 6.49333 18.3675C5.21 18.5142 3.8875 18.03 2.92917 17.0717C1.97167 16.1133 1.48833 14.7883 1.60417 13.4367C0.595 12.6333 0 11.355 0 10C0 8.645 0.595 7.36667 1.63333 6.49333C1.4875 5.2125 1.97083 3.8875 2.92917 2.92917C3.8875 1.97083 5.21 1.485 6.56333 1.60417C7.36667 0.595833 8.645 0 10 0C11.355 0 12.6333 0.595 13.5067 1.6325C14.7917 1.48833 16.1125 1.97 17.0708 2.92833C18.0283 3.88667 18.5117 5.21167 18.3958 6.56333C19.405 7.36667 20 8.645 20 10C20 11.355 19.405 12.6333 18.3667 13.5067C18.5125 14.7875 18.0292 16.1125 17.0708 17.0708C16.1117 18.0292 14.7867 18.5092 13.4367 18.3958C12.6333 19.4042 11.355 20 10 20ZM6.56083 16.73C7.02833 16.73 7.46167 16.9308 7.7675 17.2933C8.32417 17.9542 9.1375 18.3333 10 18.3333C10.8625 18.3333 11.6758 17.9542 12.2325 17.2933C12.5683 16.8942 13.0575 16.6925 13.5792 16.735C14.4408 16.8075 15.2825 16.5017 15.8925 15.8917C16.5017 15.2825 16.8092 14.4392 16.7358 13.5783C16.6908 13.0583 16.8942 12.5675 17.2942 12.2308C17.9542 11.675 18.3333 10.8608 18.3333 9.99917C18.3333 9.1375 17.9542 8.32333 17.2942 7.7675C16.895 7.43167 16.6908 6.94 16.7358 6.42C16.8092 5.55917 16.5025 4.71583 15.8925 4.10667C15.2825 3.4975 14.4358 3.195 13.58 3.26333C13.0583 3.31 12.5683 3.10417 12.2325 2.70583C11.6758 2.045 10.8625 1.66583 10 1.66583C9.1375 1.66583 8.32417 2.045 7.7675 2.70583C7.43083 3.105 6.94 3.305 6.42083 3.26417C5.555 3.18917 4.7175 3.4975 4.1075 4.1075C3.49833 4.71667 3.19083 5.56 3.26417 6.42083C3.30917 6.94083 3.10583 7.43167 2.70583 7.76833C2.04583 8.32417 1.66667 9.13833 1.66667 10C1.66667 10.8617 2.04583 11.6758 2.70583 12.2317C3.105 12.5675 3.30917 13.0592 3.26417 13.5792C3.19083 14.44 3.4975 15.2833 4.1075 15.8925C4.7175 16.5017 5.56833 16.805 6.42 16.7358C6.4675 16.7317 6.51417 16.73 6.56083 16.73ZM10.6675 12.6125L14.6167 8.80583C14.9475 8.48667 14.9575 7.95833 14.6375 7.6275C14.3183 7.29667 13.7908 7.28667 13.4592 7.60583L9.49917 11.4233C9.17333 11.7492 8.6475 11.7492 8.29917 11.4025L6.40083 9.63833C6.06583 9.32583 5.5375 9.34417 5.22333 9.68167C4.91 10.0183 4.92917 10.5458 5.26667 10.8592L7.14333 12.6025C7.63167 13.0908 8.27417 13.335 8.915 13.335C9.5525 13.335 10.1867 13.0942 10.6683 12.6133L10.6675 12.6125Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2152'
        >
          <rect
            width='20'
            height='20'
            fill='white'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default CheckMarkInCircleIcon;
