import * as React from "react";

function HomeWithHeart(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      {...props}
    >
      <g
        clip-path='url(#clip0_1_2172)'
      >
        <path
          d='M10.1133 15.9193C9.68667 15.9193 9.26083 15.7768 8.905 15.4918C7.7375 14.5568 5.52917 12.281 5.52917 10.3593C5.52917 8.78266 6.74417 7.50099 8.2375 7.50099C8.97917 7.50099 9.63333 7.76599 10.1125 8.21099C10.5917 7.76682 11.2458 7.50099 11.9875 7.50099C13.4808 7.50099 14.6958 8.78349 14.6958 10.3593C14.6958 12.2818 12.4883 14.5577 11.3208 15.4927C10.965 15.7777 10.5392 15.9193 10.1133 15.9193ZM8.2375 9.16682C7.66333 9.16682 7.19583 9.70182 7.19583 10.3585C7.19583 11.4218 8.79333 13.2668 9.94667 14.1902C10.045 14.271 10.1792 14.271 10.2792 14.1902C11.4325 13.2668 13.0292 11.4218 13.0292 10.3577C13.0292 9.70016 12.5617 9.16599 11.9875 9.16599C11.4692 9.16599 10.9458 9.47766 10.9458 10.1735C10.9458 10.6335 10.5733 11.0068 10.1125 11.0068C9.65167 11.0068 9.27917 10.6335 9.27917 10.1735C9.27917 9.42932 8.7175 9.16599 8.2375 9.16599V9.16682ZM15.8333 20.0002H4.16667C1.86917 20.0002 0 18.131 0 15.8335V8.10349C0 6.71682 0.685833 5.42599 1.83583 4.64932L7.66917 0.712656C9.08417 -0.242344 10.915 -0.243177 12.3317 0.712656L18.165 4.64849C19.3142 5.42432 20 6.71516 20 8.10266V15.8327C20 18.1302 18.1308 20.0002 15.8333 20.0002ZM10.0008 1.66516C9.51333 1.66516 9.02667 1.80849 8.60167 2.09516L2.76833 6.03099C2.07833 6.49682 1.66667 7.27099 1.66667 8.10349V15.8335C1.66667 17.2118 2.78833 18.3335 4.16667 18.3335H15.8333C17.2117 18.3335 18.3333 17.2118 18.3333 15.8335V8.10349C18.3333 7.27099 17.9217 6.49599 17.2325 6.03099L11.3992 2.09516C10.9742 1.80849 10.4875 1.66516 10.0008 1.66516Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath
          id='clip0_1_2172'
        >
          <rect
            width='20'
            height='20'
            fill='white'
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default HomeWithHeart;
