import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  padding: "20px 10px",
  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 40px",
    },
  },
});

export const formWrapper = style({
  padding: 24,
  borderRadius: 24,
  backgroundColor: theme.colors.primary.softWhite,
  display: "flex",
  flexDirection: "column",
  gap: 16,
  "@media": {
    [breakpoints.tablet]: {
      gap: 32,
    },
  },
});

export const formGrid = style({
  display: "grid",
  gridTemplateColumns: "1fr",
  gap: 16,

  "@media": {
    [breakpoints.tablet]: {
      gridTemplateColumns: "1fr 1fr",
      gap: 32,
    },
  },
});

export const title = style({
  marginBottom: 20,

  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
    },
  },
});

export const radioButtonWrapper = style({
  maxWidth: 175,
  display: "flex",
  flexWrap: "wrap",
  gap: 10,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "100%",
    },
  },
});

export const submitButton = style({
  width: "100% !important",
  gridColumn: "span 2",
  "@media": {
    [breakpoints.tablet]: {
      width: "max-content",
    },
  },
});

export const field = style({
  marginBottom: 20,
});

export const textareaWrapper = style({
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  gap: 32,
});

export const checkButton = style({
  padding: "0 14px !important",
  opacity: 0.8,
  borderColor: "#DEDED6 !important",
});

export const successContent = style({
  display: "flex",
  flexDirection: "column",
  gap: 24,
  alignItems: "center",
  textAlign: "center",
  padding: "48px 24px",
});

export const errorContent = style({
  display: "flex",
  flexDirection: "column",
  gap: 24,
  alignItems: "center",
  textAlign: "center",
  padding: "48px 24px",
});

export const errorContentTitle = style({
  color: theme.colors.primary.error,
});
