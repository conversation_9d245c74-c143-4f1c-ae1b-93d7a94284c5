import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  padding: "20px 10px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "20px 0",
    },
  },
});

export const title = style({
  position: "static",
  marginBottom: 20,
  maxWidth: 540,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 0,
      position: "absolute",
      top: "50%",
      left: 40,
      transform: "translateY(-50%)",
    },
  },
});

export const img = style({
  width: 1400,
  maxWidth: "100%",
  maxHeight: 160,
  height: "auto",
  objectFit: "cover",
  borderRadius: 26,

  "@media": {
    [breakpoints.tablet]: {
      maxHeight: 600,
    },
  },
});

export const imgWrapper = style({
  position: "relative",
});

export const description = style({
  minWidth: "fit-content",
  maxWidth: "100%",

  "@media": {
    [breakpoints.tablet]: {
      minWidth: 340,
    },
  },
});

export const infoWrapper = style({
  marginTop: 20,
  padding: 24,
  display: "flex",
  flexWrap: "wrap",
  gap: 30,
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: 22,
  "@media": {
    [breakpoints.desktop]: {
      flexWrap: "nowrap",
    },
  },
});

export const greenCard = style({
  marginTop: 20,
  padding: 20,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.asidGreen,
});

export const greyCard = style({
  marginBottom: 20,
  padding: 20,
  borderRadius: 16,
  backgroundColor: theme.colors.primary.ivory,
});

export const whiteCard = style({
  display: "flex",
  flexDirection: "column",
  backgroundColor: "#fff",
  fontWeight: 400,
});

export const buttonText = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "block",
    },
  },
});

export const buttonTextMobile = style({
  display: "block",
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

export const memberTextWrapper = style({
  minWidth: "fit-content",
  maxWidth: "100%",
  "@media": {
    [breakpoints.tablet]: {
      minWidth: 484,
    },
  },
});
