"use client";
import classNames from "classnames";
import Container from "@/components/Container";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Typography from "@/components/Typography";
import IconButton from "@/components/IconButton";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { theme } from "@/styles/themes.css";
import * as styles from "./CustomersSaySlider.css";
import { PrismicNextImage } from "@prismicio/next";

/**
 * Props for `CustomersSaySlider`.
 */
export type CustomersSaySliderProps =
  SliceComponentProps<Content.CustomersSaySliderSlice>;

/**
 * Component for "CustomersSaySlider" Slices.
 */
const CustomersSaySlider = ({
  slice,
}: CustomersSaySliderProps): JSX.Element => {
  return (
    <Container
      removeBorderRadius
    >
      <section
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
        className={styles.root}
      >
        <div
          className={styles.headWrapper}
        >
          <Typography
            variant='h3'
            fontFamily='primary'
            className={styles.title}
          >
            <PrismicRichText
              field={slice.primary.title}
              components={{
                strong: ({ children }) => (
                  <span
                    style={{ fontWeight: 700 }}
                  >{children}</span>
                ),
              }}
            />
          </Typography>

          <div
            className={styles.navWrapper}
          >
            <IconButton
              title='Last Reviews'
              color={"primary"}
              id='customer-say-prev-slide'
            >
              <ChevronIcon
                turn={"left"}
                style={{ fontSize: 26 }}
              />
            </IconButton>
            <IconButton
              title='Previous Reviews'
              color={"primary"}
              id='customer-say-next-slide'
            >
              <ChevronIcon
                turn={"right"}
                style={{ fontSize: 26 }}
              />
            </IconButton>
          </div>
        </div>

        <Swiper
          modules={[Navigation]}
          navigation={{
            nextEl: "#customer-say-next-slide",
            prevEl: "#customer-say-prev-slide",
          }}
          slidesPerView={1}
          spaceBetween={20}
        >
          {slice.items.map(
            ({ avatar, testimonial, customername, customerlocation }, idx) => (
              <SwiperSlide
                key={idx}
              >
                <div
                  className={styles.slide}
                >
                  <div
                    className={classNames(
                      styles.imageWrapper,
                      styles.onlyDesktop
                    )}
                  >
                    <PrismicNextImage
                      className={styles.image}
                      field={avatar}
                    />
                  </div>
                  <div
                    className={styles.contentWrapper}
                  >
                    <Typography
                      variant='h5'
                      className={classNames(
                        styles.testimonialText,
                        styles.fixedHeight
                      )}
                    >
                      &ldquo;
                      <PrismicRichText
                        field={testimonial}
                        components={{
                          paragraph: ({ children }) => <span>{children}</span>,
                        }}
                      />{" "}
                      &rdquo;
                    </Typography>

                    <div
                      className={styles.testimonialWrapper}
                    >
                      <div
                        className={classNames(
                          styles.imageWrapper,
                          styles.onlyMobile
                        )}
                      >
                        <PrismicNextImage
                          className={styles.image}
                          field={avatar}
                        />
                      </div>
                      <Typography
                        variant='h5'
                        fontFamily='primary'
                        as={"div"}
                        className={styles.testimonialText}
                      >
                        <PrismicRichText
                          field={customername}
                          components={{
                            paragraph: ({ children }) => (
                              <div
                                className={styles.infoText}
                              >
                                &ndash;{" "}
                                <span
                                  style={{
                                    fontWeight: 700,
                                    color: theme.colors.primary.asidGreen,
                                  }}
                                >
                                  {children}
                                </span>
                                ,{" "}
                              </div>
                            ),
                          }}
                        />
                        <PrismicRichText
                          field={customerlocation}
                          components={{
                            paragraph: ({ children }) => (
                              <>
                                <p
                                  className={styles.infoText}
                                >{children}</p>
                              </>
                            ),
                          }}
                        />
                      </Typography>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            )
          )}
        </Swiper>
      </section>
    </Container>
  );
};

export default CustomersSaySlider;
