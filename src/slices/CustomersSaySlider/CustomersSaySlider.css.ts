import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";

export const root = style({
  padding: "20px 10px",
  maxWidth: "100%",
  overflow: "hidden",
  margin: "0 auto",
  "@media": {
    [breakpoints.tablet]: {
      padding: "40px",
    },
  },
});

export const slide = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 24,
  padding: 20,
  display: "flex",
  gap: 48,
  "@media": {
    [breakpoints.tablet]: {
      padding: 32,
    },
    [breakpoints.desktop]: {
      padding: 40,
    },
  },
});

export const title = style({
  maxWidth: 280,
  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "fit-content",
    },
  },
});

export const headWrapper = style({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 20,
  "@media": {
    [breakpoints.tablet]: {
      marginBottom: 40,
    },
  },
});

export const contentWrapper = style({
  height: 264,
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  flex: 1,
  gap: 20,
  "@media": {
    [breakpoints.tablet]: {
      height: 300,
    },
  },
});

export const navWrapper = style({
  display: "flex",
  gap: 16,
  alignSelf: "flex-end",
});

export const imageWrapper = style({
  flexShrink: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: "50%",
  width: 100,
  height: 100,
  overflow: "hidden",
  "@media": {
    [breakpoints.tablet]: {
      width: 200,
      height: 200,
    },
    [breakpoints.desktop]: {
      width: 300,
      height: 300,
    },
  },
});

export const image = style({
  objectFit: "cover",
  width: "100%",
  height: "100%",
  borderRadius: "50%",
});

export const testimonialText = style({
  color: theme.colors.primary.softWhite,
  fontSize: "20px !important",
  lineHeight: "120% !important",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "32px !important",
    },
    [breakpoints.desktop]: {
      fontSize: "40px !important",
    },
  },
});
export const testimonialWrapper = style({
  display: "flex",
  alignItems: "center",
  gap: 20,
  justifyContent: "space-between",
  "@media": {
    [breakpoints.tablet]: {
      justifyContent: "flex-end",
    },
  },
});

export const fixedHeight = style({
  maxHeight: 240,
  overflow: "hidden",
});

export const customerName = style({
  fontStyle: "italic",
  color: theme.colors.primary.asidGreen,
  fontWeight: 500,
});

export const onlyMobile = style({
  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

export const onlyDesktop = style({
  display: "none",
  "@media": {
    [breakpoints.tablet]: {
      display: "block",
    },
  },
});

export const infoText = style({
  display: "block",
  textAlign: "right",
  "@media": {
    [breakpoints.tablet]: {
      display: "inline",
    },
  },
});
